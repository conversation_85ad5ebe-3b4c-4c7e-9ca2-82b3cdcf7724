#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片尺寸调整工具
将图片调整为3:2比例，支持JPG、JPEG、PNG格式
确保文件大小在2MB以内，保持原始文件名
"""

from pathlib import Path
from PIL import Image

class ImageResizer:
    def __init__(self, input_dir="feature-cards-png", output_dir="resized_images"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 目标比例 3:2
        self.target_ratio = 3 / 2
        # 最大文件大小 2MB
        self.max_file_size = 2 * 1024 * 1024
        # 支持的格式
        self.supported_formats = {'.jpg', '.jpeg', '.png'}
        
    def calculate_crop_box(self, width, height):
        """计算裁剪框以获得3:2比例"""
        current_ratio = width / height
        
        if current_ratio > self.target_ratio:
            # 图片太宽，需要裁剪宽度
            new_width = int(height * self.target_ratio)
            left = (width - new_width) // 2
            return (left, 0, left + new_width, height)
        else:
            # 图片太高，需要裁剪高度
            new_height = int(width / self.target_ratio)
            top = (height - new_height) // 2
            return (0, top, width, top + new_height)
    
    def resize_image(self, image_path, output_path):
        """调整单个图片"""
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 获取原始尺寸
                width, height = img.size
                print(f"原始尺寸: {width}x{height}")
                
                # 计算裁剪框
                crop_box = self.calculate_crop_box(width, height)
                
                # 裁剪图片
                cropped_img = img.crop(crop_box)
                print(f"裁剪后尺寸: {cropped_img.size}")
                
                # 确保是3:2比例
                crop_width, crop_height = cropped_img.size
                if abs(crop_width / crop_height - self.target_ratio) > 0.01:
                    # 微调尺寸确保精确的3:2比例
                    if crop_width / crop_height > self.target_ratio:
                        new_width = int(crop_height * self.target_ratio)
                        cropped_img = cropped_img.resize((new_width, crop_height), Image.Resampling.LANCZOS)
                    else:
                        new_height = int(crop_width / self.target_ratio)
                        cropped_img = cropped_img.resize((crop_width, new_height), Image.Resampling.LANCZOS)
                
                # 确定输出格式
                file_ext = image_path.suffix.lower()
                if file_ext in ['.jpg', '.jpeg']:
                    format_name = 'JPEG'
                    quality = 95
                else:
                    format_name = 'PNG'
                    quality = None
                
                # 保存图片并调整质量以控制文件大小
                while quality is None or quality >= 60:
                    try:
                        if format_name == 'JPEG':
                            cropped_img.save(output_path, format=format_name, quality=quality, optimize=True)
                        else:
                            cropped_img.save(output_path, format=format_name, optimize=True)
                        
                        # 检查文件大小
                        file_size = output_path.stat().st_size
                        if file_size <= self.max_file_size:
                            print(f"最终尺寸: {cropped_img.size}, 文件大小: {file_size / 1024:.1f}KB")
                            return True
                        
                        # 删除文件准备重新保存
                        if output_path.exists():
                            output_path.unlink()
                        
                        if format_name == 'PNG':
                            # PNG格式，尝试调整图片尺寸
                            current_size = cropped_img.size
                            scale_factor = (self.max_file_size / file_size) ** 0.5
                            new_size = (int(current_size[0] * scale_factor), int(current_size[1] * scale_factor))
                            cropped_img = cropped_img.resize(new_size, Image.Resampling.LANCZOS)
                            cropped_img.save(output_path, format=format_name, optimize=True)
                            print(f"最终尺寸: {new_size}, 文件大小: {output_path.stat().st_size / 1024:.1f}KB")
                            return True
                        else:
                            # JPEG格式，降低质量
                            quality -= 5
                    
                    except Exception as e:
                        print(f"保存文件时出错: {e}")
                        if output_path.exists():
                            output_path.unlink()
                        break
                
                # 如果质量降到很低仍然太大，调整图片尺寸
                try:
                    current_size = cropped_img.size
                    scale_factor = 0.8
                    new_size = (int(current_size[0] * scale_factor), int(current_size[1] * scale_factor))
                    cropped_img = cropped_img.resize(new_size, Image.Resampling.LANCZOS)
                    cropped_img.save(output_path, format=format_name, quality=60 if format_name == 'JPEG' else None, optimize=True)
                    print(f"最终尺寸: {new_size}, 文件大小: {output_path.stat().st_size / 1024:.1f}KB")
                    return True
                except Exception as e:
                    print(f"最终保存时出错: {e}")
                    return False
                
        except Exception as e:
            print(f"处理图片 {image_path} 时出错: {e}")
            return False
    
    def process_all_images(self):
        """处理所有图片"""
        if not self.input_dir.exists():
            print(f"输入目录不存在: {self.input_dir}")
            return
        
        # 获取所有支持的图片文件
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(self.input_dir.glob(f"*{ext}"))
            image_files.extend(self.input_dir.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print("未找到支持的图片文件")
            return
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        success_count = 0
        for image_path in image_files:
            print(f"\n处理: {image_path.name}")
            
            # 保持原始文件名
            output_path = self.output_dir / image_path.name
            
            if self.resize_image(image_path, output_path):
                success_count += 1
                print(f"✓ 成功处理: {image_path.name}")
            else:
                print(f"✗ 处理失败: {image_path.name}")
        
        print(f"\n处理完成！成功处理 {success_count}/{len(image_files)} 个图片")
        print(f"输出目录: {self.output_dir.absolute()}")

def main():
    """主函数"""
    print("图片尺寸调整工具")
    print("=" * 50)
    
    # 检查PIL是否可用
    try:
        from PIL import Image
    except ImportError:
        print("错误: 需要安装Pillow库")
        print("请运行: pip install Pillow")
        return
    
    # 创建处理器并开始处理
    resizer = ImageResizer()
    resizer.process_all_images()

if __name__ == "__main__":
    main()
