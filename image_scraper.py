#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片抓取程序
从2.csv文件中读取URL，抓取页面中<ul class="piclist">中的图片
使用第一列的名称作为图片文件名
"""

import csv
import requests
from bs4 import BeautifulSoup
import os
import time
from urllib.parse import urljoin, urlparse
import re
import chardet

class ImageScraper:
    def __init__(self, csv_file='2.csv', output_dir='images'):
        self.csv_file = csv_file
        self.output_dir = output_dir
        self.session = requests.Session()
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def detect_encoding(self, file_path):
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                print(f"检测到文件编码: {encoding}")
                return encoding
        except:
            # 如果检测失败，尝试常见的中文编码
            for encoding in ['gbk', 'gb2312', 'utf-8', 'cp936']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read()
                    print(f"使用编码: {encoding}")
                    return encoding
                except:
                    continue
            return 'utf-8'  # 默认返回utf-8

    def clean_filename(self, filename):
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除多余的空格和点
        filename = filename.strip('. ')
        return filename
    
    def download_image(self, img_url, filename):
        """下载图片"""
        try:
            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()
            
            # 获取文件扩展名
            parsed_url = urlparse(img_url)
            ext = os.path.splitext(parsed_url.path)[1]
            if not ext:
                # 如果没有扩展名，尝试从Content-Type获取
                content_type = response.headers.get('content-type', '')
                if 'jpeg' in content_type or 'jpg' in content_type:
                    ext = '.jpg'
                elif 'png' in content_type:
                    ext = '.png'
                elif 'gif' in content_type:
                    ext = '.gif'
                else:
                    ext = '.jpg'  # 默认使用jpg
            
            # 清理文件名并添加扩展名
            clean_name = self.clean_filename(filename)
            filepath = os.path.join(self.output_dir, f"{clean_name}{ext}")
            
            # 如果文件已存在，添加数字后缀
            counter = 1
            original_filepath = filepath
            while os.path.exists(filepath):
                name, ext = os.path.splitext(original_filepath)
                filepath = f"{name}_{counter}{ext}"
                counter += 1
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ 下载成功: {filepath}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {img_url}: {str(e)}")
            return False
    
    def scrape_images_from_url(self, url, name):
        """从指定URL抓取图片"""
        try:
            print(f"\n正在处理: {name}")
            print(f"URL: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找<ul class="piclist">元素
            piclist = soup.find('ul', class_='piclist')
            if not piclist:
                print(f"✗ 未找到 <ul class='piclist'> 元素")
                return False
            
            # 在piclist中查找所有图片
            images = piclist.find_all('img')
            if not images:
                print(f"✗ 在piclist中未找到图片")
                return False
            
            print(f"找到 {len(images)} 张图片")
            
            # 下载第一张图片（根据需求，您可以修改为下载所有图片）
            success_count = 0
            for i, img in enumerate(images[:1]):  # 只下载第一张图片
                img_src = img.get('src') or img.get('data-src')
                if img_src:
                    # 处理相对URL
                    img_url = urljoin(url, img_src)
                    print(f"图片URL: {img_url}")
                    
                    if self.download_image(img_url, name):
                        success_count += 1
                        break  # 成功下载一张图片后退出
            
            return success_count > 0
            
        except Exception as e:
            print(f"✗ 处理URL失败 {url}: {str(e)}")
            return False
    
    def run(self):
        """运行主程序"""
        print("开始抓取图片...")
        print(f"CSV文件: {self.csv_file}")
        print(f"输出目录: {self.output_dir}")
        
        success_count = 0
        total_count = 0
        
        try:
            # 检测文件编码
            encoding = self.detect_encoding(self.csv_file)

            with open(self.csv_file, 'r', encoding=encoding) as file:
                csv_reader = csv.reader(file)

                for row in csv_reader:
                    if len(row) >= 2 and row[0].strip() and row[1].strip():
                        name = row[0].strip()
                        url = row[1].strip()

                        total_count += 1
                        if self.scrape_images_from_url(url, name):
                            success_count += 1

                        # 添加延迟，避免请求过于频繁
                        time.sleep(1)
                    
        except FileNotFoundError:
            print(f"✗ 找不到文件: {self.csv_file}")
            return
        except Exception as e:
            print(f"✗ 读取CSV文件时出错: {str(e)}")
            return
        
        print(f"\n抓取完成!")
        print(f"总计处理: {total_count} 个URL")
        print(f"成功下载: {success_count} 张图片")
        print(f"图片保存在: {os.path.abspath(self.output_dir)}")

def main():
    scraper = ImageScraper()
    scraper.run()

if __name__ == "__main__":
    main()
